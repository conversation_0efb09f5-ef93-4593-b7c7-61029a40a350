"use strict";

require('adapter-min.js');

__globalAdapter.init();

require('cocos/cocos2d-js-min.js');

require('physics-min.js');

__globalAdapter.adaptEngine();

require('./ccRequire');

require('./src/settings'); // Introduce Cocos Service here


require('./main'); // TODO: move to common
// Adjust devicePixelRatio


cc.view._maxPixelRatio = 4;

if (cc.sys.platform !== cc.sys.WECHAT_GAME_SUB) {
  // Release Image objects after uploaded gl texture
  cc.macro.CLEANUP_IMAGE_CACHE = true;
}
window.boot();

// console.log('..........cc:', cc);

cc.director.on(cc.Director.EVENT_AFTER_SCENE_LAUNCH, () => {
  console.log('场景开始渲染了');
  require('./p8-sdk-demo');

  let title = createNode('lb', null, {
    x: 20,
    y: -300,
    color: cc.Color.RED
  });

});