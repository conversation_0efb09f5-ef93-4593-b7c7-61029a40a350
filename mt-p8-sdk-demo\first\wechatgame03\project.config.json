{"description": "项目配置文件。", "setting": {"urlCheck": false, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": false, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "useIsolateContext": false, "userConfirmedUseIsolateContext": true, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "disableUseStrict": false, "showES6CompileOption": false, "useCompilerPlugins": false, "ignoreUploadUnusedFiles": true, "compileWorklet": false, "minifyWXML": true, "localPlugins": false, "condition": false, "swc": false, "disableSWC": true}, "compileType": "game", "libVersion": "3.8.9", "appid": "wx1a63e35e609eff39", "projectname": "wx-p8-sdk-demo", "packOptions": {"ignore": [], "include": []}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 4}, "simulatorPluginLibVersion": {}, "condition": {}, "isGameTourist": false}