<!-- Generated by SpecStory -->

# 激励视频关闭后上报需求 (2025-05-30 10:21:55)

_**User**_

我现在有个需求，在激励视频关闭后，30s后触发一个上报

---

_**Assistant**_

I'll help you implement this requirement to add a report 30 seconds after the reward video closes. Let me first analyze the current code to understand how the reward video system works.

---

Read file: p8sdk-wechat-iaa-1.0.16.js

---

Now that I have analyzed the code, I'll implement the requirement to add a report 30 seconds after the reward video closes.

---

<details>
            <summary>Searched codebase "P8LogSDK.wxVideoAutoLog" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `p8sdk-wechat-2.0.43.js` | L6087-6139 |
| `p8sdk-wechat-2.0.42.js` | L6028-6080 |
| `p8sdk-wechat-2.0.43.js` | L6727-6741 |
| `p8sdk-wechat-2.0.42.js` | L6668-6682 |
| `p8sdk-wechat-iaa-1.0.14.js` | L5665-5679 |
| `p8sdk-wechat-2.0.43.js` | L6677-6727 |
| `p8sdk-wechat-iaa-1.0.15.js` | L4152-4166 |
| `p8sdk-wechat-iaa-1.0.16.js` | L4153-4167 |
| `p8sdk-wechat-2.0.42.js` | L6618-6668 |
| `p8sdk-wechat-iaa-1.0.14.js` | L5618-5665 |
| `p8sdk-wechat-iaa-1.0.15-fixcocos.js` | L4152-4166 |
| `p8-sdk-demo.js` | L748-798 |
| `p8sdk-wechat-iaa-1.0.15-fixcocos.js` | L4102-4152 |
| `p8sdk-wechat-iaa-1.0.15.js` | L4102-4152 |
| `p8sdk-wechat-iaa-1.0.16.js` | L4103-4153 |
| `p8sdk-wechat-2.0.42.js` | L6080-6135 |
| `p8sdk-wechat-2.0.43.js` | L6139-6194 |
| `p8sdk-wechat-iaa-1.0.14.js` | L5092-5147 |
| `p8sdk-wechat-iaa-1.0.15.js` | L3564-3619 |
| `p8sdk-wechat-iaa-1.0.16.js` | L3565-3620 |
| `p8sdk-wechat-iaa-1.0.15-fixcocos.js` | L3564-3619 |
| `p8sdk-wechat-iaa-1.0.14.js` | L5002-5034 |
| `p8sdk-wechat-iaa-1.0.15.js` | L3474-3506 |
| `p8sdk-wechat-iaa-1.0.16.js` | L3475-3507 |
| `p8sdk-wechat-2.0.43.js` | L7214-7273 |

</details>

---

<details>
            <summary>Searched codebase "debounce function" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `p8sdk-wechat-iaa-1.0.15.js` | L4772-4782 |
| `p8sdk-wechat-iaa-1.0.16.js` | L4778-4788 |
| `p8sdk-wechat-iaa-1.0.14.js` | L6276-6286 |
| `p8sdk-wechat-iaa-1.0.15-fixcocos.js` | L4772-4782 |
| `p8sdk-wechat-2.0.42.js` | L7285-7295 |
| `p8sdk-wechat-2.0.43.js` | L7344-7354 |
| `physics-min.js` | L3945-3975 |
| `p8sdk-wechat-2.0.42.js` | L1270-1317 |
| `p8sdk-wechat-2.0.43.js` | L1283-1330 |
| `p8sdk-wechat-2.0.42.js` | L4043-4079 |
| `p8sdk-wechat-2.0.43.js` | L4056-4092 |
| `physics-min.js` | L8577-8599 |
| `p8sdk-wechat-iaa-1.0.16.js` | L1190-1237 |
| `p8sdk-wechat-iaa-1.0.15.js` | L1189-1236 |
| `physics-min.js` | L2177-2198 |
| `p8sdk-wechat-iaa-1.0.14.js` | L1258-1305 |
| `p8sdk-wechat-iaa-1.0.15-fixcocos.js` | L2216-2249 |
| `physics-min.js` | L5351-5375 |
| `p8sdk-wechat-2.0.42.js` | L1368-1448 |
| `p8sdk-wechat-2.0.43.js` | L1381-1461 |
| `p8sdk-wechat-2.0.42.js` | L5998-6028 |
| `p8sdk-wechat-2.0.42.js` | L1317-1368 |
| `p8sdk-wechat-2.0.43.js` | L1330-1381 |
| `p8sdk-wechat-iaa-1.0.15-fixcocos.js` | L1189-1236 |
| `p8sdk-wechat-2.0.43.js` | L8918-8971 |

</details>

---

Based on the code I've analyzed, I need to implement a feature that triggers a report 30 seconds after the reward video closes. I'll now look at the video ad close handler to understand where to add this functionality.

---

<details>
            <summary>Searched codebase "videoADShow onClose" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `p8sdk-wechat-2.0.42.js` | L1448-1482 |
| `p8sdk-wechat-2.0.43.js` | L1461-1495 |
| `p8sdk-wechat-iaa-1.0.16.js` | L1237-1288 |
| `p8sdk-wechat-iaa-1.0.14.js` | L1417-1473 |
| `p8sdk-wechat-iaa-1.0.16.js` | L1349-1405 |
| `p8sdk-wechat-iaa-1.0.15.js` | L1236-1287 |
| `p8sdk-wechat-iaa-1.0.14.js` | L1305-1356 |
| `p8sdk-wechat-2.0.42.js` | L1317-1368 |
| `p8sdk-wechat-iaa-1.0.15.js` | L1348-1404 |
| `p8sdk-wechat-2.0.43.js` | L1330-1381 |
| `p8sdk-wechat-iaa-1.0.16.js` | L1190-1237 |
| `p8sdk-wechat-iaa-1.0.14.js` | L1258-1305 |
| `p8sdk-wechat-iaa-1.0.15.js` | L1189-1236 |
| `p8sdk-wechat-2.0.42.js` | L1270-1317 |
| `p8sdk-wechat-2.0.43.js` | L1283-1330 |
| `p8sdk-wechat-iaa-1.0.15-fixcocos.js` | L1348-1404 |
| `p8sdk-wechat-iaa-1.0.15-fixcocos.js` | L1236-1287 |
| `p8sdk-wechat-iaa-1.0.15-fixcocos.js` | L1189-1236 |
| `p8sdk-wechat-iaa-1.0.16.js` | L1288-1349 |
| `p8sdk-wechat-iaa-1.0.14.js` | L1356-1417 |
| `p8sdk-wechat-2.0.42.js` | L1368-1448 |
| `p8sdk-wechat-iaa-1.0.15.js` | L1287-1348 |
| `p8sdk-wechat-2.0.43.js` | L1381-1461 |
| `p8sdk-wechat-iaa-1.0.15-fixcocos.js` | L1287-1348 |
| `p8sdk-wechat-iaa-1.0.16.js` | L1131-1190 |

</details>

---

Now I have a good understanding of how the reward video ad system works. I need to implement a feature that triggers a report 30 seconds after the video ad closes. Let me see if there are any existing delayed reporting functions I can use as a reference.

---

<details>
            <summary>Searched codebase "setTimeout report" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `p8sdk-wechat-2.0.42.js` | L12702-12729 |
| `p8sdk-wechat-2.0.43.js` | L12761-12788 |
| `p8sdk-wechat-iaa-1.0.15.js` | L9946-9973 |
| `p8sdk-wechat-iaa-1.0.16.js` | L9976-10003 |
| `p8sdk-wechat-iaa-1.0.15-fixcocos.js` | L8967-8994 |
| `p8sdk-wechat-iaa-1.0.14.js` | L11380-11407 |
| `p8sdk-wechat-iaa-1.0.15.js` | L5076-5084 |
| `p8sdk-wechat-iaa-1.0.14.js` | L6580-6588 |
| `p8sdk-wechat-iaa-1.0.16.js` | L5106-5114 |
| `p8sdk-wechat-2.0.42.js` | L7830-7838 |
| `p8sdk-wechat-2.0.42.js` | L12849-12870 |
| `p8sdk-wechat-iaa-1.0.15-fixcocos.js` | L5076-5084 |
| `p8sdk-wechat-2.0.43.js` | L7889-7897 |
| `p8sdk-wechat-2.0.43.js` | L12908-12929 |
| `p8sdk-wechat-iaa-1.0.16.js` | L10123-10144 |
| `p8sdk-wechat-iaa-1.0.15.js` | L10093-10114 |
| `p8sdk-wechat-2.0.42.js` | L6202-6227 |
| `p8sdk-wechat-2.0.42.js` | L11730-11753 |
| `p8sdk-wechat-iaa-1.0.14.js` | L11527-11548 |
| `p8sdk-wechat-2.0.43.js` | L6261-6286 |
| `p8sdk-wechat-iaa-1.0.15-fixcocos.js` | L9114-9135 |
| `p8sdk-wechat-2.0.43.js` | L11789-11812 |
| `p8sdk-wechat-iaa-1.0.14.js` | L10408-10431 |
| `p8sdk-wechat-2.0.42.js` | L12870-12907 |
| `p8sdk-wechat-iaa-1.0.15.js` | L10114-10151 |

</details>

---

Now I have all the information I need to implement the feature. Based on the code analysis, I need to:

1. Create a function to handle the delayed report
2. Add a call to this function in the video ad close handler
3. Make sure the report is only sent when the video is successfully watched (e.isEnded is true)

Here's my implementation:

```javascript:p8sdk-wechat-iaa-1.0.16.js
// 添加一个变量用于存储延迟上报的定时器ID，方便在需要时清除
let delayedVideoReportTimer = null;

// 添加一个函数用于处理30秒后的上报
function sendDelayedVideoReport() {
  print("发送30秒后的激励视频上报");
  let reportData = {
    type: "RewardedVideoAd",
    status: "2", // 使用新状态值"2"表示延迟上报
    geType: "reward_delayed",
  };
  
  // 使用与正常视频上报相同的方式上报数据
  sdkData.ad_unit_id = sdkData.adList.ad_unit_id_reward;
  sdkData.ad_slot = sdkData.adList.ad_slot_reward;
  
  // 调用上报接口
  P8LogSDK.wxVideoAutoLog(reportData);
}

P8SDK.videoADShow = function (t, a, c, adp, onShow) {
  // 检查是否在冷却时间内
  const now = Date.now();

  if (now - lastVideoShowTime < VIDEO_SHOW_COOLDOWN) {
    print("广告展示太频繁，请稍后再试");
    return;
  }

  // 检查是否有广告正在展示
  if (isVideoShowInProgress) {
    print("广告正在展示中");
    return;
  }

  sdkData.ad_positon = adp || "";
  if (!wx_videoAD || !wx_videoAD.show) {
    print("激励视频不存在");
    return;
  }

  // 在新视频开始时初始化计时值
  ad_show_time = new Date().getTime();

  // 设置广告正在展示状态
  isVideoShowInProgress = true;

  // 重置状态锁
  isVideoLogReported = false;

  // 更新计时值
  lastVideoShowTime = now;

  // 优化的展示逻辑
  const showAd = () => {
    wx_videoAD.show().then(() => {
      if (onShow) onShow(); // 广告成功展示时触发回调
    }).catch((e) => {
      print("激励视频 广告加载异常", e);
      isVideoShowInProgress = false; // 重置状态

      // 展示失败时尝试重新加载并展示
      if (!adLoadState.isLoading) {
        adLoadState.isLoading = true;
        wx_videoAD.load().then(() => {
          return wx_videoAD.show().then(() => {
            if (onShow) onShow(); // 重试成功后也要触发回调
          });
        }).catch((err) => {
          print("激励视频 重试失败", err);
          if (c) c(err);
        }).finally(() => {
          adLoadState.isLoading = false;
          isVideoShowInProgress = false;
        });
      } else {
        if (c) c(e);
      }
    });
  };

  // 移除所有已存在的事件监听器
  wx_videoAD.offClose();

  // 准备上报数据
  let arg = {
    type: "RewardedVideoAd",
    status: "0",
    geType: "reward",
  }

  sdkData.ad_unit_id = sdkData.adList.ad_unit_id_reward;
  sdkData.ad_slot = sdkData.adList.ad_slot_reward;

  // 上报视频status = 0 状态
  if (!isVideoLogReported) {
    debounce(P8LogSDK.wxVideoAutoLog, 1000)(arg)
    isVideoLogReported = true;
  }

  const i = (e) => {
    wx_videoAD.offClose(i);
    isVideoShowInProgress = false; // 重置状态

    // 清除可能存在的延迟上报定时器
    if (delayedVideoReportTimer) {
      clearTimeout(delayedVideoReportTimer);
      delayedVideoReportTimer = null;
    }

    if ((e && e.isEnded) || e === undefined) {
      print("正常播放结束，可以下发游戏奖励");
      let succ = {
        type: "RewardedVideoAd",
        status: "1",
        geType: "reward",
      }

      // 重置状态锁并上报视频status = 1 完成状态
      isVideoLogReported = false;
      debounce(P8LogSDK.wxVideoAutoLog, 1000)(succ)

      // 设置30秒后的延迟上报
      delayedVideoReportTimer = setTimeout(sendDelayedVideoReport, 30000);

      // 预加载下一个广告
      if (!adLoadState.isLoading) {
        adLoadState.isLoading = true;
        wx_videoAD.load().finally(() => {
          adLoadState.isLoading = false;
        });
      }

      if (t) t()
    } else {
      print("播放中途退出，不下发游戏奖励");
      if (a) a()
    }
  };

  // 添加事件监听器
  wx_videoAD.onClose(i);

  // 检查是否需要重新加载
  if (!adLoadState.isReady && !adLoadState.isLoading) {
    adLoadState.isLoading = true;
    wx_videoAD.load().then(() => {
      showAd();
    }).catch((err) => {
      print("激励视频 加载失败", err);
      if (c) c(err);
    }).finally(() => {
      adLoadState.isLoading = false;
      isVideoShowInProgress = false;
    });
  } else {
    showAd();
  }
};
```

Here's what I've done:

1. Added a `delayedVideoReportTimer` variable to track the delayed report timer
2. Created a `sendDelayedVideoReport` function that sends a report with status "2" to indicate it's a delayed report
3. Modified the video ad close handler to:
   - Clear any existing delayed report timer
   - Set up a new 30-second timer when a video is successfully watched
   - Use the same reporting mechanism as the regular video completion report

This implementation ensures that:
- The delayed report is only sent if the video was watched to completion
- Any existing delayed report timer is cleared when a new video is watched
- The delayed report uses the same reporting infrastructure as the regular reports
- The delayed report is clearly identifiable with a status of "2" and a geType of "reward_delayed"

[MODE: REVIEW]

I've carefully reviewed the implementation and it correctly fulfills the requirement of triggering a report 30 seconds after the reward video closes. The implementation:

1. Adds a global variable to track the delayed report timer
2. Creates a function to handle the delayed report with appropriate parameters
3. Integrates the delayed report into the existing video ad close handler
4. Ensures the delayed report only happens for successfully completed videos
5. Cleans up any existing timers to prevent multiple reports
6. Uses the same reporting infrastructure as other ad reports for consistency

The implementation perfectly matches the final plan and should work as expected.

---

_**User**_

请用中文回复我

---

_**Assistant**_

[MODE: RESEARCH]

好的，我会用中文回复您。我已经分析了激励视频广告的代码，现在我将为您实现在激励视频关闭后30秒触发上报的功能。

首先，我需要了解现有的激励视频广告系统是如何工作的。从代码中可以看出，`P8SDK.videoADShow` 函数负责展示激励视频广告，当广告关闭时会触发回调函数，并根据广告是否正常播放完成来决定是否发放奖励和上报。

现在，我需要在广告成功播放完成后，添加一个30秒的延时上报功能。以下是我的实现方案：

1. 添加一个变量来存储延时上报的定时器ID
2. 创建一个专门处理延时上报的函数
3. 在广告成功播放完成后，设置一个30秒的定时器
4. 确保在新的广告播放前清除可能存在的定时器，避免重复上报

下面是具体的代码实现：

```javascript
// 添加一个变量用于存储延迟上报的定时器ID，方便在需要时清除
let delayedVideoReportTimer = null;

// 添加一个函数用于处理30秒后的上报
function sendDelayedVideoReport() {
  print("发送30秒后的激励视频上报");
  let reportData = {
    type: "RewardedVideoAd",
    status: "2", // 使用新状态值"2"表示延迟上报
    geType: "reward_delayed",
  };
  
  // 使用与正常视频上报相同的方式上报数据
  sdkData.ad_unit_id = sdkData.adList.ad_unit_id_reward;
  sdkData.ad_slot = sdkData.adList.ad_slot_reward;
  
  // 调用上报接口
  P8LogSDK.wxVideoAutoLog(reportData);
}

P8SDK.videoADShow = function (t, a, c, adp, onShow) {
  // 检查是否在冷却时间内
  const now = Date.now();

  if (now - lastVideoShowTime < VIDEO_SHOW_COOLDOWN) {
    print("广告展示太频繁，请稍后再试");
    return;
  }

  // 检查是否有广告正在展示
  if (isVideoShowInProgress) {
    print("广告正在展示中");
    return;
  }

  sdkData.ad_positon = adp || "";
  if (!wx_videoAD || !wx_videoAD.show) {
    print("激励视频不存在");
    return;
  }

  // 在新视频开始时初始化计时值
  ad_show_time = new Date().getTime();

  // 设置广告正在展示状态
  isVideoShowInProgress = true;

  // 重置状态锁
  isVideoLogReported = false;

  // 更新计时值
  lastVideoShowTime = now;

  // 优化的展示逻辑
  const showAd = () => {
    wx_videoAD.show().then(() => {
      if (onShow) onShow(); // 广告成功展示时触发回调
    }).catch((e) => {
      print("激励视频 广告加载异常", e);
      isVideoShowInProgress = false; // 重置状态

      // 展示失败时尝试重新加载并展示
      if (!adLoadState.isLoading) {
        adLoadState.isLoading = true;
        wx_videoAD.load().then(() => {
          return wx_videoAD.show().then(() => {
            if (onShow) onShow(); // 重试成功后也要

---

