# P8 微信小程序 SDK

## 项目简介
这是一个用于微信小程序的SDK工具包，提供了丰富的功能接口和工具方法，帮助开发者快速集成和开发微信小程序应用。

## 主要功能

### 1. 核心功能
- SDK初始化 (`getUrlConfig`)
- 心跳机制 (`txSdkHeartBeat`)
- 用户信息管理 (`getUserInfo`)
- 支付功能集成 (`midasPay`, `madeOrder`)
- 数据统计和分析

### 2. 广告相关
- Banner广告创建 (`BannerAdCreate`)
- 自定义广告创建 (`CustomAdCreate`)
- 广告配置管理 (`getAdvConf`)

### 3. 页面展示
- 弹窗页面展示 (`showCCPopupPage`, `showCCPopupPageNew`)
- 盒子页面展示 (`showCCBoxPage`, `showCCBoxPageNew`)
- 二维码提示展示 (`showCodeUrlTips`, `showCodeUrlTipsNew`)

### 4. 网络请求
- 通用请求方法 (`wxRequest`, `wxRequestShort`)
- 登录请求 (`wxRequestLoginShort`)
- 配置请求 (`wxRequestConFigShort`)

### 5. 数据存储
- 本地数据存储 (`getItem`, `setItem`, `clearItem`)
- 设备信息管理 (`getDeviceCode`, `getDevice`)

### 6. 安全与加密
- MD5加密 (`md5`)
- 数据加解密 (`decrypt`)

### 7. 分享功能
- 分享统计 (`shareApiIaa`, `postShareCount`)
- 分享落地页 (`shareFallsApiIaa`, `postShareFallsCount`)

## 使用方法

### 1. 初始化SDK
```javascript
initTXSDK({
    appid: "你的小程序APPID",
    site: "站点ID",
    aid: "应用ID",
    // 其他配置项...
});
```

### 2. 用户登录
```javascript
getUserInfo({
    success: (res) => {
        // 处理登录成功
    },
    fail: (err) => {
        // 处理登录失败
    }
});
```

### 3. 支付功能
```javascript
midasPay({
    // 支付参数配置
    // ...
});
```

## 注意事项
1. 使用前请确保已经在微信小程序后台完成相关配置
2. 需要正确配置APPID和相关密钥
3. 部分功能可能需要特定的小程序权限
4. 建议在开发环境下充分测试各项功能

## 系统要求
- 微信小程序基础库版本 >= 2.0.0
- 支持微信开发者工具最新版本

## 版本信息
当前版本：2.0.35

## 技术支持
如有问题请联系技术支持团队

## 许可证
版权所有 © 2024 